<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Attendance_m extends MY_Model
{
    protected string $_table_name = 'attendance';
    function __construct() {
        parent::__construct();
    }

    public function get_attendance_data($date){
        $attendance = parent::get(['date' => $date])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $attendance_data[$item['user_id']]['attendance'] = $item['attendance'];
            $attendance_data[$item['user_id']]['remarks'] = $item['remarks'];
            $attendance_data[$item['user_id']]['off_date'] = $item['off_date'];
        }
        return $attendance_data;
    }

    public function get_attendance_data_overview($from_date, $to_date){
        $attendance = parent::get(['date>=' => $from_date, 'date<=' => $to_date])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $attendance_data[$item['user_id']][$item['date']]['attendance'] = $item['attendance'];
            $attendance_data[$item['user_id']][$item['date']]['remarks'] = $item['remarks'];
            $attendance_data[$item['user_id']][$item['date']]['off_date'] = $item['off_date'];
        }
        return $attendance_data;
    }

    // get employee attendance data
    public function get_employee_attendance_data($user_id, $from_date, $to_date){
        $attendance = parent::get(['date>=' => $from_date, 'date<=' => $to_date, 'user_id' => $user_id])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $attendance_data[$item['date']]['attendance'] = $item['attendance'] ?? '';
            $attendance_data[$item['date']]['remarks'] = $item['remarks'] ?? '';
            $attendance_data[$item['date']]['off_date'] = $item['off_date'] ?? '';
        }
        return $attendance_data;
    }


    public function overview_employee_report($user_id, $start_date, $end_date){
        $attendance = parent::get(['date>=' => $start_date, 'date<=' => $end_date, 'user_id' => $user_id])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $date = DateTime::createFromFormat('Y-m-d', $item['date']);
            $month = $date->format('Y-m');
            $day = $date->format('d');

            $attendance_data[$month]['data'][$day]['date'] = $item['date']?? '';
            $attendance_data[$month]['data'][$day]['attendance'] = $item['attendance'] ?? '';
            $attendance_data[$month]['data'][$day]['remarks'] = $item['remarks'] ?? '';
            $attendance_data[$month]['data'][$day]['off_date'] = $item['off_date'] ?? '';
        }
        return $attendance_data;
    }

    // Calculates the number of unique working days within a given date range.
    public function get_working_days($start_date, $end_date){
        $this->db->distinct('date');
        $attendance_days = parent::get(
            ['date>=' => $start_date, 'date<=' => $end_date],
            ['date']
        )->result_array();
        return count($attendance_days) ?? 0;
    }

    // get working days within a given date range.
    public function get_office_days($start_date, $end_date){
        $this->db->distinct('date');
        $attendance_days = parent::get(
            ['date>=' => $start_date, 'date<=' => $end_date],
            ['date']
        )->result_array();
        return array_column($attendance_days, 'date');
    }

    // Get attendance status report for date range
    public function get_attendance_status_report($start_date, $end_date) {
        // Get all users (both active and inactive)
        $this->load->model('users_m');
        $users = $this->users_m->get([
            'is_employee' => 1
        ], ['id', 'name', 'employee_status'], ['key' => 'name', 'direction' => 'ASC'])->result_array();

        // Get attendance data for the date range
        $attendance_data = parent::get([
            'date >=' => $start_date,
            'date <=' => $end_date
        ])->result_array();

        // Process attendance data
        $attendance_array = [];
        foreach ($attendance_data as $attendance) {
            $attendance_array[$attendance['user_id']][$attendance['date']] = [
                'attendance' => $attendance['attendance'],
                'remarks' => $attendance['remarks']
            ];
        }

        // Generate date array
        $date_array = get_date_array($start_date, $end_date);

        // Build attendance report
        $attendance_report = [];
        foreach ($users as $user) {
            $user_id = $user['id'];

            // Check if user has any attendance records in the date range
            $has_attendance = isset($attendance_array[$user_id]);

            // Include user if they are active OR have attendance records in the date range
            if ($user['employee_status'] == 1 || $has_attendance) {
                $attendance_report[$user_id] = [
                    'user_info' => $user,
                    'daily_attendance' => [],
                    'summary' => [
                        'P' => 0,   // Present
                        'A' => 0,   // Absent
                        'WH' => 0,  // Work From Home
                        'OF' => 0,  // Off
                        'OD' => 0,  // On Duty
                        'HD' => 0   // Half Day
                    ]
                ];

                foreach ($date_array as $date) {
                    $attendance_info = $attendance_array[$user_id][$date] ?? null;
                    $attendance_status = $attendance_info['attendance'] ?? null;
                    $remarks = $attendance_info['remarks'] ?? '';

                    $attendance_report[$user_id]['daily_attendance'][$date] = [
                        'attendance' => $attendance_status,
                        'remarks' => $remarks
                    ];

                    // Count attendance types
                    if ($attendance_status) {
                        $attendance_report[$user_id]['summary'][$attendance_status]++;
                    }
                }
            }
        }

        return $attendance_report;
    }
}
