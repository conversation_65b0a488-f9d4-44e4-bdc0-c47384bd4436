<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Attendance_report extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('users_m');
        $this->load->model('time_log_m');
        $this->load->model('time_log_type_m');
        $this->load->model('attendance_m');
    }

    public function overview_employee(){
        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');
        $user_id = $this->input->get('user_id');

        if (empty($start_date) || empty($end_date) || empty($user_id)){
            $start_date = date('Y-m-01');
            $end_date = date('Y-m-t');
            redirect("app/attendance_report/overview_employee/?start_date={$start_date}&end_date={$end_date}&working_days=26&user_id=3");
        }
        $report = $this->attendance_m->overview_employee_report($user_id, $start_date, $end_date);
        $days_array = [];
        for ($i = 1; $i <= 31; $i++) {
            $days_array[] = sprintf("%02d", $i);
        }

        $this->data['days_array']   = $days_array;
        $this->data['report']       = $report;
        $this->data['page_title']   = 'Employee Overview Report';
        $this->data['page_name']    = 'attendance_report/overview_employee';
        $this->load->view('app/index', $this->data);
    }

    public function attendance_status_report() {
        if (empty($this->input->get('start_date'))){
            $end_date = date('Y-m-d');
            if (date('H') > 10){
                $start_date = $end_date;
            }else{
                $start_date = date('Y-m-d', strtotime("-1 day"));
            }
            redirect("app/attendance_report/attendance_status_report/?start_date={$start_date}&end_date={$end_date}");
        }else{
            $start_date = $this->input->get('start_date');
            $end_date = $this->input->get('end_date');
        }

        // Get all users (both active and inactive)
        $this->data['users'] = $this->users_m->get([
            'is_employee' => 1
        ], null, ['key' => 'name', 'direction' => 'ASC'])->result_array();

        // Get attendance status report data
        $this->data['attendance_report'] = $this->attendance_m->get_attendance_status_report($start_date, $end_date);

        // Get date range array
        $this->data['date_range'] = get_date_array($start_date, $end_date);

        $this->data['start_date'] = $start_date;
        $this->data['end_date'] = $end_date;
        $this->data['page_title'] = 'Attendance Status Report';
        $this->data['page_name'] = 'attendance_report/attendance_status_report';
        $this->load->view('app/index', $this->data);
    }
    public function generate_overview_report($users, $time_log_data, $attendance_data){
        foreach($users as $key => $user){
            $users[$key]['attendance_overview'] = [
                'P' => 0,
                'A' => 0,
                'WH' => 0,
                'OF' => 0,
                'OD' => 0,
                'HD' => 0,
                'late_coming' => 0,
                'early_going' => 0,
            ];

            $users[$key]['time_log_overview'] = [
                'work_duration_seconds' => 0,
                'break_duration_seconds' => 0,
                'office_duration_seconds' => 0,
                'work_duration' => '00:00:00',
                'break_duration' => '00:00:00',
                'office_duration' => '00:00:00',
            ];


            // attendance status
            $user_attendance_data = $attendance_data[$user['id']];
            if (is_array($user_attendance_data)){

                foreach ($user_attendance_data as $date => $attendance){
                    if ($attendance['attendance'] == 'P'){
                        $users[$key]['attendance_overview']['P']++;
                    }elseif ($attendance['attendance'] == 'A'){
                        $users[$key]['attendance_overview']['A']++;
                    }elseif ($attendance['attendance'] == 'WH'){
                        $users[$key]['attendance_overview']['WH']++;
                    }elseif ($attendance['attendance'] == 'OF'){
                        $users[$key]['attendance_overview']['OF']++;
                    }elseif ($attendance['attendance'] == 'OD'){
                        $users[$key]['attendance_overview']['OD']++;
                    }elseif ($attendance['attendance'] == 'HD'){
                        $users[$key]['attendance_overview']['HD']++;
                    }
                }
            }

            // time log status
            $user_time_log_data = $time_log_data[$user['id']];
            if(is_array($user_time_log_data)){
                foreach ($user_time_log_data as $date => $time_log){
                    $users[$key]['time_log_overview']['work_duration_seconds'] += $time_log['work_duration_seconds'];
                    $users[$key]['time_log_overview']['break_duration_seconds'] += $time_log['break_duration_seconds'];
                    $users[$key]['time_log_overview']['office_duration_seconds'] += $time_log['office_duration_seconds'];
                    if (is_late_coming($time_log)){
                        $users[$key]['attendance_overview']['late_coming']++;
                    }

                    if (is_early_going($time_log)){
                        $users[$key]['attendance_overview']['early_going']++;
                    }
                }
            }
            $users[$key]['time_log_overview']['work_duration'] = get_time_from_seconds($users[$key]['time_log_overview']['work_duration_seconds']);
            $users[$key]['time_log_overview']['break_duration'] = get_time_from_seconds($users[$key]['time_log_overview']['break_duration_seconds']);
            $users[$key]['time_log_overview']['office_duration'] = get_time_from_seconds($users[$key]['time_log_overview']['office_duration_seconds']);



            // calculate employee points
            $employee_points = 0;
            $employee_points = $users[$key]['time_log_overview']['work_duration_seconds']/25200;

            $employee_points = $employee_points - ($users[$key]['attendance_overview']['late_coming']*0.142857);
            $employee_points = $employee_points - ($users[$key]['attendance_overview']['early_going']*0.142857);
            $employee_points = $employee_points - ($users[$key]['attendance_overview']['WH']*0.5);
            $users[$key]['employee_points'] = number_format($employee_points, 4);

        }
        usort($users, function($a, $b) {
            return $b['time_log_overview']['work_duration_seconds'] <=> $a['time_log_overview']['work_duration_seconds'];
        });
        return $users;
    }

}